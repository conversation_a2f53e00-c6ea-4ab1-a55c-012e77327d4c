[{"id": "p1", "alumno_id": "a1", "monto": 5000, "fecha_pago": "2024-07-01", "mes": 7, "año": 2024, "metodo_pago": "Efectivo", "estado": "<PERSON><PERSON>"}, {"id": "p2", "alumno_id": "a2", "monto": 5000, "fecha_pago": "2024-06-15", "mes": 6, "año": 2024, "metodo_pago": "Transferencia", "estado": "<PERSON><PERSON>"}, {"id": "p3", "alumno_id": "a3", "monto": 5000, "fecha_pago": "2024-07-05", "mes": 7, "año": 2024, "metodo_pago": "Mercado <PERSON>", "estado": "Pendiente"}, {"alumno": "<PERSON><PERSON><PERSON>", "alumno_id": "f53b839a-7008-4bf2-b5e5-9e1a3f5684cd", "mes": "mayo", "monto": 30000, "fecha_pago": "2024-05-31", "id": "0eff213c-e6e8-461b-bd4e-362a66a6a474"}, {"id": "f0f53afe-3954-46ef-8df1-9e8776d5aac1", "alumno_id": "97611024-09e8-4d90-a94a-4bb81b7c7c9e", "fecha": "2024-06-07", "monto": 123000}, {"id": "a82e64dd-e5e4-4342-bc2b-f2af6015f034", "alumno_id": "4f149c02-be54-4114-a5f1-28c0844e3205", "fecha": "2025-05-21", "monto": 15000}]