{"name": "registros-gimnasia", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@headlessui/react": "^2.2.2", "@heroicons/react": "^2.1.1", "@supabase/supabase-js": "^2.39.3", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.17.19", "@tanstack/react-query-devtools": "^5.17.19", "@tiptap/extension-highlight": "^2.12.0", "@tiptap/extension-image": "^2.12.0", "@tiptap/extension-link": "^2.12.0", "@tiptap/extension-placeholder": "^2.12.0", "@tiptap/extension-text-align": "^2.12.0", "@tiptap/extension-underline": "^2.12.0", "@tiptap/pm": "^2.12.0", "@tiptap/react": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "chart.js": "^4.4.1", "date-fns": "^3.3.1", "jspdf": "^2.5.1", "jspdf-autotable": "^3.7.0", "next": "^14.2.28", "papaparse": "^5.5.2", "react": "^18", "react-chartjs-2": "^5.2.0", "react-datepicker": "^5.1.0", "react-dom": "^18", "react-hot-toast": "^2.5.2", "react-swipeable": "^7.0.2", "react-tooltip": "^5.28.1", "task-master-ai": "^0.12.1", "zod": "^3.22.4"}, "devDependencies": {"@eslint/js": "^9.26.0", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@types/jest": "^29.5.14", "@types/node": "^20.17.48", "@types/papaparse": "^5.3.16", "@types/react": "^18.3.21", "@types/react-datepicker": "^4.19.5", "@types/react-dom": "^18.3.7", "@types/react-tooltip": "^3.11.0", "@types/testing-library__jest-dom": "^5.14.9", "@types/testing-library__react": "^10.0.1", "@types/testing-library__user-event": "^4.1.1", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "autoprefixer": "^10.0.1", "cypress": "^14.3.3", "eslint": "^8.57.0", "eslint-config-next": "^14.2.3", "eslint-plugin-react": "^7.37.5", "globals": "^16.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8", "tailwindcss": "^3.3.0", "ts-jest": "^29.3.4", "typescript": "^5", "vitest": "^3.1.3"}}