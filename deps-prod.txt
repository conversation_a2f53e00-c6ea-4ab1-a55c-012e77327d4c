registros-gimnasia@0.1.0 /Users/<USER>/Documents/GitHub/Registros-de-gimnasia
├─┬ @headlessui/react@2.2.2
│ ├─┬ @floating-ui/react@0.26.28
│ │ ├── @floating-ui/react-dom@2.1.2
│ │ ├── @floating-ui/utils@0.2.9
│ │ ├── react-dom@18.3.1 deduped
│ │ ├── react@18.3.1 deduped
│ │ └── tabbable@6.2.0
│ ├─┬ @react-aria/focus@3.20.2
│ │ ├── @react-aria/interactions@3.25.0 deduped
│ │ ├── @react-aria/utils@3.28.2
│ │ ├── @react-types/shared@3.29.0
│ │ ├── @swc/helpers@0.5.5 deduped
│ │ ├── clsx@2.1.1
│ │ ├── react-dom@18.3.1 deduped
│ │ └── react@18.3.1 deduped
│ ├─┬ @react-aria/interactions@3.25.0
│ │ ├── @react-aria/ssr@3.9.8
│ │ ├── @react-aria/utils@3.28.2 deduped
│ │ ├── @react-stately/flags@3.1.1
│ │ ├── @react-types/shared@3.29.0 deduped
│ │ ├── @swc/helpers@0.5.5 deduped
│ │ ├── react-dom@18.3.1 deduped
│ │ └── react@18.3.1 deduped
│ ├─┬ @tanstack/react-virtual@3.13.8
│ │ ├── @tanstack/virtual-core@3.13.8
│ │ ├── react-dom@18.3.1 deduped
│ │ └── react@18.3.1 deduped
│ ├── react-dom@18.3.1 deduped
│ ├── react@18.3.1 deduped
│ └─┬ use-sync-external-store@1.5.0
│   └── react@18.3.1 deduped
├─┬ @heroicons/react@2.2.0
│ └── react@18.3.1 deduped
├─┬ @supabase/supabase-js@2.49.4
│ ├─┬ @supabase/auth-js@2.69.1
│ │ └── @supabase/node-fetch@2.6.15 deduped
│ ├─┬ @supabase/functions-js@2.4.4
│ │ └── @supabase/node-fetch@2.6.15 deduped
│ ├─┬ @supabase/node-fetch@2.6.15
│ │ └── whatwg-url@5.0.0
│ ├─┬ @supabase/postgrest-js@1.19.4
│ │ └── @supabase/node-fetch@2.6.15 deduped
│ ├─┬ @supabase/realtime-js@2.11.2
│ │ ├── @supabase/node-fetch@2.6.15 deduped
│ │ ├── @types/phoenix@1.6.6
│ │ ├── @types/ws@8.18.1
│ │ └── ws@8.18.2
│ └─┬ @supabase/storage-js@2.7.1
│   └── @supabase/node-fetch@2.6.15 deduped
├─┬ @tailwindcss/typography@0.5.16
│ ├── lodash.castarray@4.4.0
│ ├── lodash.isplainobject@4.0.6
│ ├── lodash.merge@4.6.2
│ ├─┬ postcss-selector-parser@6.0.10
│ │ ├── cssesc@3.0.0
│ │ └── util-deprecate@1.0.2
│ └─┬ tailwindcss@3.4.17
│   ├── @alloc/quick-lru@5.2.0
│   ├── arg@5.0.2
│   ├── chokidar@3.6.0
│   ├── didyoumean@1.2.2
│   ├── dlv@1.1.3
│   ├── fast-glob@3.3.3
│   ├── glob-parent@6.0.2
│   ├── is-glob@4.0.3
│   ├── jiti@1.21.7
│   ├── lilconfig@3.1.3
│   ├── micromatch@4.0.8
│   ├── normalize-path@3.0.0
│   ├── object-hash@3.0.0
│   ├── picocolors@1.1.1
│   ├── postcss-import@15.1.0
│   ├── postcss-js@4.0.1
│   ├── postcss-load-config@4.0.2
│   ├── postcss-nested@6.2.0
│   ├── postcss-selector-parser@6.1.2
│   ├── postcss@8.5.3
│   ├── resolve@1.22.10
│   └── sucrase@3.35.0
├─┬ @tanstack/react-query-devtools@5.75.7
│ ├── @tanstack/query-devtools@5.74.7
│ ├── @tanstack/react-query@5.75.7 deduped
│ └── react@18.3.1 deduped
├─┬ @tanstack/react-query@5.75.7
│ ├── @tanstack/query-core@5.75.7
│ └── react@18.3.1 deduped
├─┬ @tiptap/extension-highlight@2.12.0
│ └─┬ @tiptap/core@2.12.0
│   └── @tiptap/pm@2.12.0 deduped
├─┬ @tiptap/extension-image@2.12.0
│ └── @tiptap/core@2.12.0 deduped
├─┬ @tiptap/extension-link@2.12.0
│ ├── @tiptap/core@2.12.0 deduped
│ ├── @tiptap/pm@2.12.0 deduped
│ └── linkifyjs@4.3.1
├─┬ @tiptap/extension-placeholder@2.12.0
│ ├── @tiptap/core@2.12.0 deduped
│ └── @tiptap/pm@2.12.0 deduped
├─┬ @tiptap/extension-text-align@2.12.0
│ └── @tiptap/core@2.12.0 deduped
├─┬ @tiptap/extension-underline@2.12.0
│ └── @tiptap/core@2.12.0 deduped
├─┬ @tiptap/pm@2.12.0
│ ├─┬ prosemirror-changeset@2.3.0
│ │ └── prosemirror-transform@1.10.4 deduped
│ ├─┬ prosemirror-collab@1.3.1
│ │ └── prosemirror-state@1.4.3 deduped
│ ├─┬ prosemirror-commands@1.7.1
│ │ ├── prosemirror-model@1.25.1 deduped
│ │ ├── prosemirror-state@1.4.3 deduped
│ │ └── prosemirror-transform@1.10.4 deduped
│ ├─┬ prosemirror-dropcursor@1.8.2
│ │ ├── prosemirror-state@1.4.3 deduped
│ │ ├── prosemirror-transform@1.10.4 deduped
│ │ └── prosemirror-view@1.39.2 deduped
│ ├─┬ prosemirror-gapcursor@1.3.2
│ │ ├── prosemirror-keymap@1.2.3 deduped
│ │ ├── prosemirror-model@1.25.1 deduped
│ │ ├── prosemirror-state@1.4.3 deduped
│ │ └── prosemirror-view@1.39.2 deduped
│ ├─┬ prosemirror-history@1.4.1
│ │ ├── prosemirror-state@1.4.3 deduped
│ │ ├── prosemirror-transform@1.10.4 deduped
│ │ ├── prosemirror-view@1.39.2 deduped
│ │ └── rope-sequence@1.3.4
│ ├─┬ prosemirror-inputrules@1.5.0
│ │ ├── prosemirror-state@1.4.3 deduped
│ │ └── prosemirror-transform@1.10.4 deduped
│ ├─┬ prosemirror-keymap@1.2.3
│ │ ├── prosemirror-state@1.4.3 deduped
│ │ └── w3c-keyname@2.2.8
│ ├─┬ prosemirror-markdown@1.13.2
│ │ ├── @types/markdown-it@14.1.2
│ │ ├── markdown-it@14.1.0
│ │ └── prosemirror-model@1.25.1 deduped
│ ├─┬ prosemirror-menu@1.2.5
│ │ ├── crelt@1.0.6
│ │ ├── prosemirror-commands@1.7.1 deduped
│ │ ├── prosemirror-history@1.4.1 deduped
│ │ └── prosemirror-state@1.4.3 deduped
│ ├─┬ prosemirror-model@1.25.1
│ │ └── orderedmap@2.1.1
│ ├─┬ prosemirror-schema-basic@1.2.4
│ │ └── prosemirror-model@1.25.1 deduped
│ ├─┬ prosemirror-schema-list@1.5.1
│ │ ├── prosemirror-model@1.25.1 deduped
│ │ ├── prosemirror-state@1.4.3 deduped
│ │ └── prosemirror-transform@1.10.4 deduped
│ ├─┬ prosemirror-state@1.4.3
│ │ ├── prosemirror-model@1.25.1 deduped
│ │ ├── prosemirror-transform@1.10.4 deduped
│ │ └── prosemirror-view@1.39.2 deduped
│ ├─┬ prosemirror-tables@1.7.1
│ │ ├── prosemirror-keymap@1.2.3 deduped
│ │ ├── prosemirror-model@1.25.1 deduped
│ │ ├── prosemirror-state@1.4.3 deduped
│ │ ├── prosemirror-transform@1.10.4 deduped
│ │ └── prosemirror-view@1.39.2 deduped
│ ├─┬ prosemirror-trailing-node@3.0.0
│ │ ├── @remirror/core-constants@3.0.0
│ │ ├── escape-string-regexp@4.0.0
│ │ ├── prosemirror-model@1.25.1 deduped
│ │ ├── prosemirror-state@1.4.3 deduped
│ │ └── prosemirror-view@1.39.2 deduped
│ ├─┬ prosemirror-transform@1.10.4
│ │ └── prosemirror-model@1.25.1 deduped
│ └─┬ prosemirror-view@1.39.2
│   ├── prosemirror-model@1.25.1 deduped
│   ├── prosemirror-state@1.4.3 deduped
│   └── prosemirror-transform@1.10.4 deduped
├─┬ @tiptap/react@2.12.0
│ ├── @tiptap/core@2.12.0 deduped
│ ├─┬ @tiptap/extension-bubble-menu@2.12.0
│ │ ├── @tiptap/core@2.12.0 deduped
│ │ ├── @tiptap/pm@2.12.0 deduped
│ │ └── tippy.js@6.3.7
│ ├─┬ @tiptap/extension-floating-menu@2.12.0
│ │ ├── @tiptap/core@2.12.0 deduped
│ │ ├── @tiptap/pm@2.12.0 deduped
│ │ └── tippy.js@6.3.7 deduped
│ ├── @tiptap/pm@2.12.0 deduped
│ ├── @types/use-sync-external-store@0.0.6
│ ├── fast-deep-equal@3.1.3
│ ├── react-dom@18.3.1 deduped
│ ├── react@18.3.1 deduped
│ └── use-sync-external-store@1.5.0 deduped
├─┬ @tiptap/starter-kit@2.12.0
│ ├── @tiptap/core@2.12.0 deduped
│ ├─┬ @tiptap/extension-blockquote@2.12.0
│ │ └── @tiptap/core@2.12.0 deduped
│ ├─┬ @tiptap/extension-bold@2.12.0
│ │ └── @tiptap/core@2.12.0 deduped
│ ├─┬ @tiptap/extension-bullet-list@2.12.0
│ │ └── @tiptap/core@2.12.0 deduped
│ ├─┬ @tiptap/extension-code-block@2.12.0
│ │ ├── @tiptap/core@2.12.0 deduped
│ │ └── @tiptap/pm@2.12.0 deduped
│ ├─┬ @tiptap/extension-code@2.12.0
│ │ └── @tiptap/core@2.12.0 deduped
│ ├─┬ @tiptap/extension-document@2.12.0
│ │ └── @tiptap/core@2.12.0 deduped
│ ├─┬ @tiptap/extension-dropcursor@2.12.0
│ │ ├── @tiptap/core@2.12.0 deduped
│ │ └── @tiptap/pm@2.12.0 deduped
│ ├─┬ @tiptap/extension-gapcursor@2.12.0
│ │ ├── @tiptap/core@2.12.0 deduped
│ │ └── @tiptap/pm@2.12.0 deduped
│ ├─┬ @tiptap/extension-hard-break@2.12.0
│ │ └── @tiptap/core@2.12.0 deduped
│ ├─┬ @tiptap/extension-heading@2.12.0
│ │ └── @tiptap/core@2.12.0 deduped
│ ├─┬ @tiptap/extension-history@2.12.0
│ │ ├── @tiptap/core@2.12.0 deduped
│ │ └── @tiptap/pm@2.12.0 deduped
│ ├─┬ @tiptap/extension-horizontal-rule@2.12.0
│ │ ├── @tiptap/core@2.12.0 deduped
│ │ └── @tiptap/pm@2.12.0 deduped
│ ├─┬ @tiptap/extension-italic@2.12.0
│ │ └── @tiptap/core@2.12.0 deduped
│ ├─┬ @tiptap/extension-list-item@2.12.0
│ │ └── @tiptap/core@2.12.0 deduped
│ ├─┬ @tiptap/extension-ordered-list@2.12.0
│ │ └── @tiptap/core@2.12.0 deduped
│ ├─┬ @tiptap/extension-paragraph@2.12.0
│ │ └── @tiptap/core@2.12.0 deduped
│ ├─┬ @tiptap/extension-strike@2.12.0
│ │ └── @tiptap/core@2.12.0 deduped
│ ├─┬ @tiptap/extension-text-style@2.12.0
│ │ └── @tiptap/core@2.12.0 deduped
│ ├─┬ @tiptap/extension-text@2.12.0
│ │ └── @tiptap/core@2.12.0 deduped
│ └── @tiptap/pm@2.12.0 deduped
├─┬ chart.js@4.4.9
│ └── @kurkle/color@0.3.4
├── date-fns@3.6.0
├─┬ jspdf-autotable@3.8.4
│ └── jspdf@2.5.2 deduped
├─┬ jspdf@2.5.2
│ ├── @babel/runtime@7.27.1
│ ├── atob@2.1.2
│ ├── btoa@1.2.1
│ ├─┬ canvg@3.0.11
│ │ ├── @babel/runtime@7.27.1 deduped
│ │ ├── @types/raf@3.4.3
│ │ ├── core-js@3.42.0 deduped
│ │ ├── raf@3.4.1
│ │ ├── regenerator-runtime@0.13.11
│ │ ├── rgbcolor@1.0.1
│ │ ├── stackblur-canvas@2.7.0
│ │ └── svg-pathdata@6.0.3
│ ├── core-js@3.42.0
│ ├── dompurify@2.5.8
│ ├── fflate@0.8.2
│ └─┬ html2canvas@1.4.1
│   ├── css-line-break@2.1.0
│   └── text-segmentation@1.0.3
├─┬ next@14.2.28
│ ├── @next/env@14.2.28
│ ├── @next/swc-darwin-arm64@14.2.28
│ ├── UNMET OPTIONAL DEPENDENCY @next/swc-darwin-x64@14.2.28
│ ├── UNMET OPTIONAL DEPENDENCY @next/swc-linux-arm64-gnu@14.2.28
│ ├── UNMET OPTIONAL DEPENDENCY @next/swc-linux-arm64-musl@14.2.28
│ ├── UNMET OPTIONAL DEPENDENCY @next/swc-linux-x64-gnu@14.2.28
│ ├── UNMET OPTIONAL DEPENDENCY @next/swc-linux-x64-musl@14.2.28
│ ├── UNMET OPTIONAL DEPENDENCY @next/swc-win32-arm64-msvc@14.2.28
│ ├── UNMET OPTIONAL DEPENDENCY @next/swc-win32-ia32-msvc@14.2.28
│ ├── UNMET OPTIONAL DEPENDENCY @next/swc-win32-x64-msvc@14.2.28
│ ├── UNMET OPTIONAL DEPENDENCY @opentelemetry/api@^1.1.0
│ ├── UNMET OPTIONAL DEPENDENCY @playwright/test@^1.41.2
│ ├─┬ @swc/helpers@0.5.5
│ │ ├── @swc/counter@0.1.3
│ │ └── tslib@2.8.1
│ ├─┬ busboy@1.6.0
│ │ └── streamsearch@1.1.0
│ ├── caniuse-lite@1.0.30001717
│ ├── graceful-fs@4.2.11
│ ├─┬ postcss@8.4.31
│ │ ├── nanoid@3.3.11
│ │ ├── picocolors@1.1.1 deduped
│ │ └── source-map-js@1.2.1
│ ├── react-dom@18.3.1 deduped
│ ├── react@18.3.1 deduped
│ ├── UNMET OPTIONAL DEPENDENCY sass@^1.3.0
│ └─┬ styled-jsx@5.1.1
│   ├── client-only@0.0.1
│   └── react@18.3.1 deduped
├── papaparse@5.5.2
├─┬ react-chartjs-2@5.3.0
│ ├── chart.js@4.4.9 deduped
│ └── react@18.3.1 deduped
├─┬ react-datepicker@5.1.0
│ ├── @floating-ui/react@0.26.28 deduped
│ ├── classnames@2.5.1
│ ├─┬ date-fns@2.30.0
│ │ └── @babel/runtime@7.27.1 deduped
│ ├─┬ prop-types@15.8.1
│ │ ├── loose-envify@1.4.0 deduped
│ │ ├── object-assign@4.1.1
│ │ └── react-is@16.13.1
│ ├── react-dom@18.3.1 deduped
│ ├─┬ react-onclickoutside@6.13.2
│ │ ├── react-dom@18.3.1 deduped
│ │ └── react@18.3.1 deduped
│ └── react@18.3.1 deduped
├─┬ react-dom@18.3.1
│ ├─┬ loose-envify@1.4.0
│ │ └── js-tokens@4.0.0
│ ├── react@18.3.1 deduped
│ └─┬ scheduler@0.23.2
│   └── loose-envify@1.4.0 deduped
├─┬ react-hot-toast@2.5.2
│ ├── csstype@3.1.3
│ ├─┬ goober@2.1.16
│ │ └── csstype@3.1.3 deduped
│ ├── react-dom@18.3.1 deduped
│ └── react@18.3.1 deduped
├─┬ react-swipeable@7.0.2
│ └── react@18.3.1 deduped
├─┬ react-tooltip@5.28.1
│ ├─┬ @floating-ui/dom@1.7.0
│ │ ├── @floating-ui/core@1.7.0
│ │ └── @floating-ui/utils@0.2.9 deduped
│ ├── classnames@2.5.1 deduped
│ ├── react-dom@18.3.1 deduped
│ └── react@18.3.1 deduped
├─┬ react@18.3.1
│ └── loose-envify@1.4.0 deduped
├─┬ task-master-ai@0.12.1
│ ├─┬ @anthropic-ai/sdk@0.39.0
│ │ ├── @types/node-fetch@2.6.12
│ │ ├── @types/node@18.19.103
│ │ ├── abort-controller@3.0.0
│ │ ├── agentkeepalive@4.6.0
│ │ ├── form-data-encoder@1.7.2
│ │ ├── formdata-node@4.4.1
│ │ └── node-fetch@2.7.0
│ ├─┬ boxen@8.0.1
│ │ ├── ansi-align@3.0.1
│ │ ├── camelcase@8.0.0
│ │ ├── chalk@5.4.1
│ │ ├── cli-boxes@3.0.0
│ │ ├── string-width@7.2.0
│ │ ├── type-fest@4.41.0
│ │ ├── widest-line@5.0.0
│ │ └── wrap-ansi@9.0.0
│ ├─┬ chalk@4.1.2
│ │ ├── ansi-styles@4.3.0
│ │ └── supports-color@7.2.0
│ ├─┬ cli-table3@0.6.5
│ │ ├── @colors/colors@1.5.0
│ │ └── string-width@4.2.3
│ ├── commander@11.1.0
│ ├─┬ cors@2.8.5
│ │ ├── object-assign@4.1.1 deduped
│ │ └── vary@1.1.2
│ ├── dotenv@16.5.0
│ ├─┬ express@4.21.2
│ │ ├── accepts@1.3.8
│ │ ├── array-flatten@1.1.1
│ │ ├── body-parser@1.20.3
│ │ ├── content-disposition@0.5.4
│ │ ├── content-type@1.0.5
│ │ ├── cookie-signature@1.0.6
│ │ ├── cookie@0.7.1
│ │ ├── debug@2.6.9
│ │ ├── depd@2.0.0
│ │ ├── encodeurl@2.0.0
│ │ ├── escape-html@1.0.3
│ │ ├── etag@1.8.1
│ │ ├── finalhandler@1.3.1
│ │ ├── fresh@0.5.2
│ │ ├── http-errors@2.0.0
│ │ ├── merge-descriptors@1.0.3
│ │ ├── methods@1.1.2
│ │ ├── on-finished@2.4.1
│ │ ├── parseurl@1.3.3
│ │ ├── path-to-regexp@0.1.12
│ │ ├── proxy-addr@2.0.7
│ │ ├── qs@6.13.0
│ │ ├── range-parser@1.2.1
│ │ ├── safe-buffer@5.2.1
│ │ ├── send@0.19.0
│ │ ├── serve-static@1.16.2
│ │ ├── setprototypeof@1.2.0
│ │ ├── statuses@2.0.1
│ │ ├── type-is@1.6.18
│ │ ├── utils-merge@1.0.1
│ │ └── vary@1.1.2 deduped
│ ├─┬ fastmcp@1.27.6
│ │ ├── @modelcontextprotocol/sdk@1.11.1
│ │ ├── @standard-schema/spec@1.0.0
│ │ ├── execa@9.5.3
│ │ ├── file-type@20.5.0
│ │ ├── fuse.js@7.1.0 deduped
│ │ ├── mcp-proxy@2.14.2
│ │ ├── strict-event-emitter-types@2.0.0
│ │ ├── undici@7.9.0
│ │ ├── uri-templates@0.2.0
│ │ ├── xsschema@0.2.0-beta.3
│ │ ├── yargs@17.7.2
│ │ ├── zod-to-json-schema@3.24.5
│ │ └── zod@3.24.4 deduped
│ ├── figlet@1.8.1
│ ├── fuse.js@7.1.0
│ ├─┬ gradient-string@3.0.0
│ │ ├── chalk@5.4.1
│ │ └── tinygradient@1.1.5
│ ├── helmet@8.1.0
│ ├─┬ inquirer@12.6.1
│ │ ├── @inquirer/core@10.1.11
│ │ ├── @inquirer/prompts@7.5.1
│ │ ├── @inquirer/type@3.0.6
│ │ ├── @types/node@20.17.50
│ │ ├── ansi-escapes@4.3.2
│ │ ├── mute-stream@2.0.0
│ │ ├── run-async@3.0.0
│ │ └── rxjs@7.8.2
│ ├─┬ jsonwebtoken@9.0.2
│ │ ├── jws@3.2.2
│ │ ├── lodash.includes@4.3.0
│ │ ├── lodash.isboolean@3.0.3
│ │ ├── lodash.isinteger@4.0.4
│ │ ├── lodash.isnumber@3.0.3
│ │ ├── lodash.isplainobject@4.0.6 deduped
│ │ ├── lodash.isstring@4.0.1
│ │ ├── lodash.once@4.1.1
│ │ ├── ms@2.1.3
│ │ └── semver@7.7.2
│ ├── lru-cache@10.4.3
│ ├─┬ openai@4.98.0
│ │ ├── @types/node-fetch@2.6.12 deduped
│ │ ├── @types/node@18.19.103
│ │ ├── abort-controller@3.0.0 deduped
│ │ ├── agentkeepalive@4.6.0 deduped
│ │ ├── form-data-encoder@1.7.2 deduped
│ │ ├── formdata-node@4.4.1 deduped
│ │ ├── node-fetch@2.7.0 deduped
│ │ ├── ws@8.18.2 deduped
│ │ └── zod@3.24.4 deduped
│ ├─┬ ora@8.2.0
│ │ ├── chalk@5.4.1
│ │ ├── cli-cursor@5.0.0
│ │ ├── cli-spinners@2.9.2
│ │ ├── is-interactive@2.0.0
│ │ ├── is-unicode-supported@2.1.0
│ │ ├── log-symbols@6.0.0
│ │ ├── stdin-discarder@0.2.2
│ │ ├── string-width@7.2.0
│ │ └── strip-ansi@7.1.0
│ └── uuid@11.1.0
└── zod@3.24.4

